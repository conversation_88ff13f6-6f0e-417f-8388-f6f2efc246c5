<{if $dataList}>
<{foreach from=$dataList item=item}>
<div class="list-item">
  <div class="<{if $title == '待发货'}>order-item-container flex-row<{/if}>">
    <{if $title == '待发货'}>
    <div class="checkbox-container">
      <input type="checkbox" class="order-checkbox" value="<{$item.delivery_id}>"
             data-status="<{$item.status}>"
             data-process-status="<{$item.process_status}>"
             data-has-logi-no="<{if $item.delivery_bill.logi_no}>1<{else}>0<{/if}>"
             data-print-status="<{$item.delivery_bill.print_status}>"
             onchange="toggleOrderSelect(this)" checked>
    </div>
    <{/if}>
    <div class="<{if $title == '待发货'}>order-content flex-col<{/if}>" <{if $title == '待发货'}>style="flex: 1;"<{/if}>>
      <div class="text-wrapper_5 flex-row justify-between">
        <span class="text_11">订单号：</span>
        <span class="text_12"><{$item.order_bn}>
          <{if $item.order_info.is_change_order}>
            <span style="color: #FF0000">换货</span>
          <{/if}>
        </span>
        <span class="status-order"><{$item.dly_shipping_status}></span>
        <{if $item.is_history == 'true'}>
          <span class="status-order">历史</span>
        <{/if}>
      </div>
      <div class="text-wrapper_5 flex-row justify-between">
        <span class="text_11">门店：</span>
        <span class="text_12"><{$item.shop_info.store_name}></span>
      </div>
      <div class="text-wrapper_5 flex-row justify-between">
        <span class="text_11">渠道：</span>
        <span class="text_12"><{$item.shop_type_name}></span>
        <{if $item.order_info.order_over_time}>
        <span class="text_13" style="color: #FF0000"><{$item.order_info.order_over_time}></span>
        <{elseif $item.order_info.deadline}>
        <span class="text_13 countdown" data-deadline="<{$item.order_info.deadline}>"></span>
        <{/if}>
      </div>
      <div class="text-wrapper_5 flex-row justify-between">
        <span class="text_11">发货单号：</span>
        <span class="text_12"><{$item.outer_delivery_bn}></span>
      </div>
      <{if $item.delivery_bill.logi_no}>
        <div class="text-wrapper_5 flex-row justify-between">
        <span class="text_11">快递单号：</span>
        <span class="text_12"><{$item.delivery_bill.logi_no}></span>
        <{if in_array($item.logi_status,['1','2','5','6','7'])}>
        <span class="text_13" style="color: rgba(38, 120, 213, 1);" onclick="showUpdateLogiNoModal('<{$item.delivery_id}>','<{$item.delivery_bill.logi_no}>')"> 修改物流单号 </span>
        <{/if}>
      </div>
      <{/if}>
      <div class="text-wrapper_5 flex-row justify-between">
        <span class="text_11">下单时间：</span>
        <span class="text_12"><{$item.order_info.createtime|date:"Y-m-d H:i:s"}></span>
      </div>
      <div class="text-wrapper_5 flex-row pr">
        <span class="text_11">收件人：</span>
        <span class="text_26 sensitive-text flex-shrink-1" data-decryptAddress="false" data-orderid="<{$item.order_info.order_id}>" data-masked-copy="<{$item.ship_name}>（<{$item.ship_mobile}>） <{$item.ship_addr}> " data-raw-copy="<{$item.ship_name}>（<{$item.ship_mobile}>） <{$item.ship_addr}> "  data-raw="<{$item.ship_name}>（<{$item.ship_mobile}>）" data-masked="<{$item.ship_name}>（<{$item.ship_mobile}>）"><{$item.ship_name}>（<{$item.ship_mobile}>）</span>
        <span class="text_27 copy-btn" data-copy="<{$item.ship_name}>（<{$item.ship_mobile}>） <{$item.ship_addr}>">复制</span>
        <div class="toggle-visibility label_1">
          <i class="eye-icon hidden"></i>
        </div>
      </div>
      <div class="text-wrapper_5 flex-row">
        <span class="text_11">收货地址：</span>
        <span class="text_26 sensitive-text flex-shrink-1" data-raw="<{$item.ship_addr}>" data-masked="<{$item.ship_addr}>"><{$item.ship_addr}></span>
        <!--
        <span class="text_27 copy-btn" data-copy="<{$item.ship_addr}>">复制</span>
        -->
      </div>
      <{if $item.order_info.custom_remark_text}>
      <div class="text-wrapper_5 flex-row">
        <span class="text_11">买家备注：</span>
        <div class="flex-row align-center justify-between" style="flex: 1;">
          <span class="remark-content"><{$item.order_info.custom_remark_text}></span>
          <span class="arrow-icon"></span>
        </div>
      </div>
      <{/if}>

      <{if $item.order_info.order_remark_text}>
      <div class="text-wrapper_5 flex-row">
        <span class="text_11">客服备注：</span>
        <div class="flex-row align-center justify-between" style="flex: 1;">
          <span class="remark-content"><{$item.order_info.order_remark_text}></span>
          <span class="arrow-icon"></span>
        </div>
      </div>
      <{/if}>
      <div class="hr"></div>
    </div>
  </div>
  <!-- goodsInfo -->

  <{if $item.delivery_items}>
  <{foreach from=$item.delivery_items item=dly_item}>
  <div class="section_6 flex-row">
    <{if $dly_item.default_img_url}>
    <img class="image_2" referrerpolicy="no-referrer"
      src="<{$dly_item.default_img_url}>" />
    <{else}>
    <img class="image_2" referrerpolicy="no-referrer"
      src="<{$env.app.res_url}>/img/nopic.jpg" />
    <{/if}>
    <div class="text-group_1 flex-col">
      <div class="status-info"> <span class="text_28 flex-shrink-1"><{$dly_item.product_name}></span> </div>
      <span class="text_29">商户货号：<{$dly_item.busness_material_bn}></span>
      <span class="text_29">子订单号：<{$dly_item.oid}></span>
      <span class="text_30"><{$dly_item.number}>件；<{$dly_item.product_attr}></span>
    </div>
  </div>
  <div class="section_6 justify-between flex-wrap">
        <div class="mrg-side-4">
          <span class="text_36">买家</span><span class="text_37">￥<{$dly_item.divide_order_fee}></span>
        </div>
        <div class="mrg-side-4">
          <span class="text_36">优惠</span><span class="text_37">￥<{$dly_item.esb_pmt_amount}></span>
        </div>
        <div class="mrg-side-4">
          <span class="text_36">商家</span><span class="text_37">￥<{$dly_item.esb_amount}></span>
        </div>
  </div>
  <{/foreach}>
  <!-- total -->
  <!--
  <div class="text-wrapper_11 flex-row">
    <span class="text_36">总价：</span>
    <span class="text_37">￥<{$item.total_amount}></span>
  </div>
  -->
  <{/if}>
  <div class="section_7 flex-row justify-end">
    <{if $item.status == 0 && $item.process_status == 0 && !$item.delivery_bill.logi_no}>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="showTrackingModal('<{$item.delivery_id}>', '<{$item.order_info.source_status}>')">补录运单号</span>
    </div>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="window.location.href='<{$delivery_link.onlineDelivery}>?delivery_id=<{$item.delivery_id}>'">呼叫快递</span>
    </div>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="window.location.href='<{$delivery_link.outOfStock}>?delivery_id=<{$item.delivery_id}>'" >缺货</span>
    </div>
    <{elseif $item.status == 0 && $item.delivery_bill.logi_no}>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="doCancelDelivery('<{$item.delivery_id}>')">取消快递</span>
    </div>
    <{if $item.delivery_bill.print_status != 3}>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="printDeliveryBill('<{$item.delivery_id}>')">补打快递单</span>
    </div>
    <{/if}>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="confirmDelivery('<{$item.delivery_id}>')">确认发货</span>
    </div>
    <{elseif $item.status == 3 && $item.delivery_bill.logi_no}>
    <div class="text-wrapper_12 flex-col">
      <span class="text_38" onclick="window.location.href='<{$delivery_link.showLogistics}>?delivery_id=<{$item.delivery_id}>'">查看物流</span>
    </div>
    <{/if}>
  </div>
</div>
<{/foreach}>
<{/if}>
