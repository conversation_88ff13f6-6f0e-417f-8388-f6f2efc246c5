# 批量操作不自动全选修改说明

## 修改目标

将批量呼叫快递、批量打单、批量发货功能修改为：点击按钮时不自动全选所有符合条件的订单，而是进入批量模式等待用户手动选择订单。

## 修改内容

### 1. 批量呼叫快递功能修改

**文件**: `app/wap/view/order/order_list.html`

**修改函数**: `checkAndGoBatchDelivery()`

**修改前**:
```javascript
// 进入批量呼叫快递模式
currentBatchMode = 'delivery';

// 应用批量呼叫快递的选择逻辑到当前已加载的订单
applyBatchDeliverySelection();

// 检查是否有符合条件的订单
if (selectedOrders.size === 0) {
  showToast('当前页面没有可呼叫快递的订单');
  hideNextStepButton();
  return;
}

// 显示右下角的"下一步"按钮
updateBatchActionButton();
```

**修改后**:
```javascript
// 进入批量呼叫快递模式
currentBatchMode = 'delivery';

// 清空当前选择，不自动全选
selectedOrders.clear();

// 启用符合条件的订单复选框，禁用不符合条件的订单复选框
enableBatchDeliveryCheckboxes();

// 更新UI状态
updateSelectAllCheckboxState();
updateSelectedCount();

// 隐藏下一步按钮，等待用户手动选择
hideNextStepButton();

// 提示用户选择订单
showToast('请选择需要呼叫快递的订单');
```

**新增函数**: `enableBatchDeliveryCheckboxes()`
```javascript
function enableBatchDeliveryCheckboxes() {
  const orderCheckboxes = document.querySelectorAll('.order-checkbox');

  orderCheckboxes.forEach(checkbox => {
    // 检查是否满足批量呼叫快递条件
    if (canSelectForBatchDelivery(checkbox)) {
      checkbox.checked = false; // 不自动选择
      checkbox.disabled = false;
    } else {
      checkbox.checked = false;
      checkbox.disabled = true;
    }
  });

  console.log('批量呼叫快递模式：已启用符合条件的订单复选框，等待用户选择');
}
```

### 2. 批量打单功能修改

**文件**: `app/wap/view/order/order_list.html`

**修改函数**: `toggleBatchPrint()`

**修改前**:
```javascript
// 应用批量打单选择逻辑到当前已加载的订单
batchPrintManager.activate();
selectedOrders = new Set(batchPrintManager.selectedOrders);

console.log('批量打单模式：选中', selectedOrders.size, '个订单');

// 检查是否有符合条件的订单
if (selectedOrders.size === 0) {
  showToast('当前页面没有可打印的订单');
  hideNextStepButton();
  return;
}

// 更新UI状态
updateSelectedCount();
updateBatchActionButton();
```

**修改后**:
```javascript
// 清空当前选择，不自动全选
selectedOrders.clear();

// 启用批量打单管理器（不自动选择）
batchPrintManager.activateWithoutAutoSelect();

// 更新UI状态
updateSelectedCount();
hideNextStepButton();

// 提示用户选择订单
showToast('请选择需要打印的订单');
```

**文件**: `app/wap/statics/js/batch-print.js`

**新增方法**: `activateWithoutAutoSelect()`
```javascript
/**
 * 激活批量打单模式（不自动选择订单）
 */
activateWithoutAutoSelect() {
  this.isActive = true;
  this.selectedOrders.clear();
  this.enableBatchPrintCheckboxes();
  this.updateUI();
  
  console.log('BatchPrintManager: 激活批量打单模式，等待用户手动选择订单');
}

/**
 * 启用批量打单模式的复选框（不自动选择）
 */
enableBatchPrintCheckboxes() {
  const orderCheckboxes = document.querySelectorAll('.order-checkbox');

  orderCheckboxes.forEach(checkbox => {
    const listItem = checkbox.closest('.list-item');

    if (this.canSelectForBatchPrint(listItem)) {
      checkbox.checked = false; // 不自动选择
      checkbox.disabled = false;
    } else {
      checkbox.checked = false;
      checkbox.disabled = true;
    }
  });

  console.log('BatchPrintManager: 已启用符合条件的订单复选框，等待用户选择');
}
```

### 3. 批量发货功能修改

**文件**: `app/wap/view/order/order_list.html`

**修改函数**: `toggleBatchConsign()`

**修改前**:
```javascript
// 激活批量发货管理器
batchConsignManager.activate();
selectedOrders = new Set(batchConsignManager.selectedOrders);

console.log('批量发货模式：选中', selectedOrders.size, '个订单');

// 检查是否有符合条件的订单
if (selectedOrders.size === 0) {
  showToast('当前页面没有可发货的订单');
  hideNextStepButton();
  return;
}

// 更新UI状态
updateSelectedCount();
updateBatchActionButton();
```

**修改后**:
```javascript
// 清空当前选择，不自动全选
selectedOrders.clear();

// 启用批量发货管理器（不自动选择）
batchConsignManager.activateWithoutAutoSelect();

// 更新UI状态
updateSelectedCount();
hideNextStepButton();

// 提示用户选择订单
showToast('请选择需要发货的订单');
```

**文件**: `app/wap/statics/js/batch-consign.js`

**新增方法**: `activateWithoutAutoSelect()`
```javascript
/**
 * 激活批量发货模式（不自动选择订单）
 */
activateWithoutAutoSelect() {
  this.isActive = true;
  this.selectedOrders.clear();
  this.enableBatchConsignCheckboxes();
  this.updateUI();
  
  console.log('BatchConsignManager: 激活批量发货模式，等待用户手动选择订单');
}

/**
 * 启用批量发货模式的复选框（不自动选择）
 */
enableBatchConsignCheckboxes() {
  const orderCheckboxes = document.querySelectorAll('.order-checkbox');

  orderCheckboxes.forEach(checkbox => {
    const listItem = checkbox.closest('.list-item');

    if (this.canSelectForBatchConsign(listItem)) {
      checkbox.checked = false; // 不自动选择
      checkbox.disabled = false;
    } else {
      checkbox.checked = false;
      checkbox.disabled = true;
    }
  });

  console.log('BatchConsignManager: 已启用符合条件的订单复选框，等待用户选择');
}
```

## 修改效果

### 修改前的行为：
1. 点击"批量呼叫快递"按钮 → 自动选择所有符合条件的订单 → 显示"下一步"按钮
2. 点击"批量打单"按钮 → 自动选择所有符合条件的订单 → 显示"下一步"按钮  
3. 点击"批量发货"按钮 → 自动选择所有符合条件的订单 → 显示"下一步"按钮

### 修改后的行为：
1. 点击"批量呼叫快递"按钮 → 进入批量模式，启用符合条件的复选框 → 提示用户选择 → 等待用户手动选择订单
2. 点击"批量打单"按钮 → 进入批量模式，启用符合条件的复选框 → 提示用户选择 → 等待用户手动选择订单
3. 点击"批量发货"按钮 → 进入批量模式，启用符合条件的复选框 → 提示用户选择 → 等待用户手动选择订单

### 用户体验改进：
- ✅ 用户可以精确控制要操作的订单
- ✅ 避免误操作大量订单
- ✅ 提供更灵活的批量操作方式
- ✅ 保持原有的条件筛选逻辑（只有符合条件的订单可以被选择）
- ✅ 保持原有的全选功能（点击全选时仍会选择所有符合条件的订单）

## 兼容性说明

- 保留了原有的 `applyBatchDeliverySelection()` 等自动全选函数，供全选功能使用
- 新增的方法不影响现有功能
- 用户仍可以通过全选按钮快速选择所有符合条件的订单
- 所有原有的条件判断逻辑保持不变
