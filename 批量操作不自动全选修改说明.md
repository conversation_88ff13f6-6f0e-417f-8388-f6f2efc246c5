# 批量操作不自动全选修改说明

## 修改目标

将批量呼叫快递、批量打单、批量发货功能修改为：
1. 点击按钮时不自动全选所有符合条件的订单
2. 保持已经选择的订单状态不变
3. 重新验证已选择订单是否符合当前批量操作条件
4. 对不符合条件的已选择订单自动取消选择并提示用户

## 修改内容

### 1. 批量呼叫快递功能修改

**文件**: `app/wap/view/order/order_list.html`

**修改函数**: `checkAndGoBatchDelivery()`

**修改前**:
```javascript
// 进入批量呼叫快递模式
currentBatchMode = 'delivery';

// 应用批量呼叫快递的选择逻辑到当前已加载的订单
applyBatchDeliverySelection();

// 检查是否有符合条件的订单
if (selectedOrders.size === 0) {
  showToast('当前页面没有可呼叫快递的订单');
  hideNextStepButton();
  return;
}

// 显示右下角的"下一步"按钮
updateBatchActionButton();
```

**修改后**:
```javascript
// 进入批量呼叫快递模式
currentBatchMode = 'delivery';

// 不清空当前选择，保持已选择的订单状态
// selectedOrders.clear(); // 注释掉这行，保持已有选择

// 启用符合条件的订单复选框，验证已选择订单的条件
enableBatchDeliveryCheckboxes();

// 更新UI状态
updateSelectAllCheckboxState();
updateSelectedCount();

// 根据选择状态决定是否显示下一步按钮
if (selectedOrders.size > 0) {
  updateBatchActionButton();
} else {
  hideNextStepButton();
}
```

**新增函数**: `enableBatchDeliveryCheckboxes()`
```javascript
function enableBatchDeliveryCheckboxes() {
  const orderCheckboxes = document.querySelectorAll('.order-checkbox');
  let validSelectedCount = 0;
  let invalidSelectedCount = 0;

  orderCheckboxes.forEach(checkbox => {
    const deliveryId = checkbox.value;
    const wasSelected = checkbox.checked;

    // 检查是否满足批量呼叫快递条件
    if (canSelectForBatchDelivery(checkbox)) {
      // 符合条件的订单
      checkbox.disabled = false;

      if (wasSelected) {
        // 保持已选择状态
        checkbox.checked = true;
        selectedOrders.add(deliveryId);
        validSelectedCount++;
      } else {
        // 未选择的订单保持未选择状态
        checkbox.checked = false;
      }
    } else {
      // 不符合条件的订单
      checkbox.disabled = true;

      if (wasSelected) {
        // 如果之前选中但现在不符合条件，取消选择并提示
        checkbox.checked = false;
        selectedOrders.delete(deliveryId);
        invalidSelectedCount++;
      } else {
        checkbox.checked = false;
      }
    }
  });

  // 提示用户选择状态
  if (invalidSelectedCount > 0) {
    showToast(`已取消${invalidSelectedCount}个不符合条件的订单选择，请重新选择需要呼叫快递的订单`);
  } else if (validSelectedCount > 0) {
    showToast(`保持${validSelectedCount}个订单的选择状态，可继续选择其他订单`);
  } else {
    showToast('请选择需要呼叫快递的订单');
  }

  console.log('批量呼叫快递模式：保持有效选择', validSelectedCount, '个，取消无效选择', invalidSelectedCount, '个');
}
```

### 2. 批量打单功能修改

**文件**: `app/wap/view/order/order_list.html`

**修改函数**: `toggleBatchPrint()`

**修改前**:
```javascript
// 应用批量打单选择逻辑到当前已加载的订单
batchPrintManager.activate();
selectedOrders = new Set(batchPrintManager.selectedOrders);

console.log('批量打单模式：选中', selectedOrders.size, '个订单');

// 检查是否有符合条件的订单
if (selectedOrders.size === 0) {
  showToast('当前页面没有可打印的订单');
  hideNextStepButton();
  return;
}

// 更新UI状态
updateSelectedCount();
updateBatchActionButton();
```

**修改后**:
```javascript
// 清空当前选择，不自动全选
selectedOrders.clear();

// 启用批量打单管理器（不自动选择）
batchPrintManager.activateWithoutAutoSelect();

// 更新UI状态
updateSelectedCount();
hideNextStepButton();

// 提示用户选择订单
showToast('请选择需要打印的订单');
```

**文件**: `app/wap/statics/js/batch-print.js`

**新增方法**: `activateWithoutAutoSelect()`
```javascript
/**
 * 激活批量打单模式（不自动选择订单）
 */
activateWithoutAutoSelect() {
  this.isActive = true;
  this.selectedOrders.clear();
  this.enableBatchPrintCheckboxes();
  this.updateUI();
  
  console.log('BatchPrintManager: 激活批量打单模式，等待用户手动选择订单');
}

/**
 * 启用批量打单模式的复选框（不自动选择）
 */
enableBatchPrintCheckboxes() {
  const orderCheckboxes = document.querySelectorAll('.order-checkbox');

  orderCheckboxes.forEach(checkbox => {
    const listItem = checkbox.closest('.list-item');

    if (this.canSelectForBatchPrint(listItem)) {
      checkbox.checked = false; // 不自动选择
      checkbox.disabled = false;
    } else {
      checkbox.checked = false;
      checkbox.disabled = true;
    }
  });

  console.log('BatchPrintManager: 已启用符合条件的订单复选框，等待用户选择');
}
```

### 3. 批量发货功能修改

**文件**: `app/wap/view/order/order_list.html`

**修改函数**: `toggleBatchConsign()`

**修改前**:
```javascript
// 激活批量发货管理器
batchConsignManager.activate();
selectedOrders = new Set(batchConsignManager.selectedOrders);

console.log('批量发货模式：选中', selectedOrders.size, '个订单');

// 检查是否有符合条件的订单
if (selectedOrders.size === 0) {
  showToast('当前页面没有可发货的订单');
  hideNextStepButton();
  return;
}

// 更新UI状态
updateSelectedCount();
updateBatchActionButton();
```

**修改后**:
```javascript
// 清空当前选择，不自动全选
selectedOrders.clear();

// 启用批量发货管理器（不自动选择）
batchConsignManager.activateWithoutAutoSelect();

// 更新UI状态
updateSelectedCount();
hideNextStepButton();

// 提示用户选择订单
showToast('请选择需要发货的订单');
```

**文件**: `app/wap/statics/js/batch-consign.js`

**新增方法**: `activateWithoutAutoSelect()`
```javascript
/**
 * 激活批量发货模式（不自动选择订单）
 */
activateWithoutAutoSelect() {
  this.isActive = true;
  this.selectedOrders.clear();
  this.enableBatchConsignCheckboxes();
  this.updateUI();
  
  console.log('BatchConsignManager: 激活批量发货模式，等待用户手动选择订单');
}

/**
 * 启用批量发货模式的复选框（不自动选择）
 */
enableBatchConsignCheckboxes() {
  const orderCheckboxes = document.querySelectorAll('.order-checkbox');

  orderCheckboxes.forEach(checkbox => {
    const listItem = checkbox.closest('.list-item');

    if (this.canSelectForBatchConsign(listItem)) {
      checkbox.checked = false; // 不自动选择
      checkbox.disabled = false;
    } else {
      checkbox.checked = false;
      checkbox.disabled = true;
    }
  });

  console.log('BatchConsignManager: 已启用符合条件的订单复选框，等待用户选择');
}
```

## 修改效果

### 修改前的行为：
1. 点击"批量呼叫快递"按钮 → 自动选择所有符合条件的订单 → 显示"下一步"按钮
2. 点击"批量打单"按钮 → 自动选择所有符合条件的订单 → 显示"下一步"按钮  
3. 点击"批量发货"按钮 → 自动选择所有符合条件的订单 → 显示"下一步"按钮

### 修改后的行为：
1. 点击"批量呼叫快递"按钮 → 进入批量模式 → 保持已选择订单状态 → 验证条件 → 智能提示用户
2. 点击"批量打单"按钮 → 进入批量模式 → 保持已选择订单状态 → 验证条件 → 智能提示用户
3. 点击"批量发货"按钮 → 进入批量模式 → 保持已选择订单状态 → 验证条件 → 智能提示用户

### 智能状态保持逻辑：
- **已选择且符合条件的订单**：保持选中状态，计入有效选择
- **已选择但不符合条件的订单**：自动取消选择，提示用户重新选择
- **未选择的订单**：保持未选择状态，等待用户手动选择
- **不符合条件的订单**：禁用复选框，无法选择

### 用户提示逻辑：
- 如果有订单被取消选择：`已取消X个不符合条件的订单选择，请重新选择需要XXX的订单`
- 如果有有效选择保持：`保持X个订单的选择状态，可继续选择其他订单`
- 如果没有任何选择：`请选择需要XXX的订单`

### 用户体验改进：
- ✅ 用户可以精确控制要操作的订单
- ✅ 避免误操作大量订单
- ✅ 提供更灵活的批量操作方式
- ✅ **智能保持已选择状态**：切换批量模式时不会丢失用户的选择
- ✅ **智能条件验证**：自动验证已选择订单是否符合新模式条件
- ✅ **智能用户提示**：根据选择状态变化提供精准的操作提示
- ✅ 保持原有的条件筛选逻辑（只有符合条件的订单可以被选择）
- ✅ 保持原有的全选功能（点击全选时仍会选择所有符合条件的订单）

## 兼容性说明

- 保留了原有的 `applyBatchDeliverySelection()` 等自动全选函数，供全选功能使用
- 新增的方法不影响现有功能
- 用户仍可以通过全选按钮快速选择所有符合条件的订单
- 所有原有的条件判断逻辑保持不变
