<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mac版微信小程序Confirm兼容性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-btn {
            width: 100%;
            height: 44px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
            cursor: pointer;
        }
        
        .test-btn:hover {
            background: #0056CC;
        }
        
        .test-btn.danger {
            background: #FF3B30;
        }
        
        .test-btn.danger:hover {
            background: #D70015;
        }
        
        .result {
            margin-top: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
            font-size: 14px;
        }
        
        /* 自定义确认弹窗样式 */
        .custom-confirm-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .custom-confirm-mask {
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .custom-confirm-container {
            position: relative;
            background: #fff;
            border-radius: 0.5rem;
            width: 18rem;
            max-width: 90%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .custom-confirm-content {
            padding: 1.5rem 1rem 1rem;
            text-align: center;
        }

        .custom-confirm-message {
            font-size: 1rem;
            color: #333;
            line-height: 1.5;
            font-weight: 400;
        }

        .custom-confirm-buttons {
            display: flex;
            border-top: 1px solid #e0e0e0;
        }

        .custom-confirm-btn {
            flex: 1;
            height: 2.75rem;
            border: none;
            background: #fff;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            -webkit-tap-highlight-color: transparent;
        }

        .custom-confirm-cancel {
            color: #666;
            border-right: 1px solid #e0e0e0;
        }

        .custom-confirm-cancel:hover,
        .custom-confirm-cancel:active {
            background-color: #f5f5f5;
        }

        .custom-confirm-ok {
            color: #007AFF;
            font-weight: 500;
        }

        .custom-confirm-ok:hover,
        .custom-confirm-ok:active {
            background-color: #f0f8ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Mac版微信小程序Confirm兼容性测试</h2>
        
        <button class="test-btn" onclick="testNativeConfirm()">测试原生confirm()</button>
        <button class="test-btn" onclick="testCustomConfirm()">测试自定义confirm()</button>
        <button class="test-btn danger" onclick="testBatchDelivery()">测试批量呼叫快递确认</button>
        
        <div class="result" id="result">
            点击按钮开始测试...
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #666;">
            <p><strong>测试说明：</strong></p>
            <ul>
                <li>在Mac版微信小程序中，原生confirm可能无法正常工作</li>
                <li>自定义confirm应该在所有环境中都能正常工作</li>
                <li>测试时请注意观察弹窗的显示和交互效果</li>
            </ul>
        </div>
    </div>

    <!-- 自定义确认弹窗 -->
    <div class="custom-confirm-modal" id="customConfirmModal" style="display: none;">
        <div class="custom-confirm-mask"></div>
        <div class="custom-confirm-container">
            <div class="custom-confirm-content">
                <div class="custom-confirm-message" id="customConfirmMessage">确认操作吗？</div>
            </div>
            <div class="custom-confirm-buttons">
                <button class="custom-confirm-btn custom-confirm-cancel" id="customConfirmCancel">取消</button>
                <button class="custom-confirm-btn custom-confirm-ok" id="customConfirmOk">确定</button>
            </div>
        </div>
    </div>

    <script>
        function updateResult(message) {
            document.getElementById('result').innerHTML = message;
        }

        function testNativeConfirm() {
            try {
                const result = confirm('这是原生confirm测试，请选择确定或取消');
                updateResult(`原生confirm结果: ${result ? '确定' : '取消'}`);
            } catch (error) {
                updateResult(`原生confirm错误: ${error.message}`);
            }
        }

        function testCustomConfirm() {
            showCustomConfirm('这是自定义confirm测试，请选择确定或取消', 
                function() {
                    updateResult('自定义confirm结果: 确定');
                },
                function() {
                    updateResult('自定义confirm结果: 取消');
                }
            );
        }

        function testBatchDelivery() {
            showCustomConfirm('确认要批量呼叫快递吗？', 
                function() {
                    updateResult('批量呼叫快递: 用户确认，开始执行批量操作...');
                },
                function() {
                    updateResult('批量呼叫快递: 用户取消操作');
                }
            );
        }

        // 自定义确认弹窗函数
        function showCustomConfirm(message, onConfirm, onCancel) {
            const modal = document.getElementById('customConfirmModal');
            const messageEl = document.getElementById('customConfirmMessage');
            const okBtn = document.getElementById('customConfirmOk');
            const cancelBtn = document.getElementById('customConfirmCancel');
            const mask = modal.querySelector('.custom-confirm-mask');

            // 设置消息
            messageEl.textContent = message;

            // 显示弹窗
            modal.style.display = 'block';

            // 清除之前的事件监听器
            const newOkBtn = okBtn.cloneNode(true);
            const newCancelBtn = cancelBtn.cloneNode(true);
            const newMask = mask.cloneNode(true);
            
            okBtn.parentNode.replaceChild(newOkBtn, okBtn);
            cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
            mask.parentNode.replaceChild(newMask, mask);

            // 确定按钮事件
            newOkBtn.addEventListener('click', function() {
                hideCustomConfirm();
                if (onConfirm && typeof onConfirm === 'function') {
                    onConfirm();
                }
            });

            // 取消按钮事件
            newCancelBtn.addEventListener('click', function() {
                hideCustomConfirm();
                if (onCancel && typeof onCancel === 'function') {
                    onCancel();
                }
            });

            // 点击遮罩关闭
            newMask.addEventListener('click', function() {
                hideCustomConfirm();
                if (onCancel && typeof onCancel === 'function') {
                    onCancel();
                }
            });

            // 添加触摸事件支持，解决移动端兼容性
            newOkBtn.addEventListener('touchstart', function(e) {
                e.preventDefault();
                this.style.opacity = '0.7';
            });

            newOkBtn.addEventListener('touchend', function(e) {
                e.preventDefault();
                this.style.opacity = '1';
                hideCustomConfirm();
                if (onConfirm && typeof onConfirm === 'function') {
                    onConfirm();
                }
            });

            newCancelBtn.addEventListener('touchstart', function(e) {
                e.preventDefault();
                this.style.opacity = '0.7';
            });

            newCancelBtn.addEventListener('touchend', function(e) {
                e.preventDefault();
                this.style.opacity = '1';
                hideCustomConfirm();
                if (onCancel && typeof onCancel === 'function') {
                    onCancel();
                }
            });
        }

        // 隐藏自定义确认弹窗
        function hideCustomConfirm() {
            const modal = document.getElementById('customConfirmModal');
            modal.style.display = 'none';
        }
    </script>
</body>
</html>
